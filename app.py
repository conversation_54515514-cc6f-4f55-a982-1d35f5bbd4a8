import tkinter as tk
from tkinter import messagebox, filedialog
import sys
import os
import psutil
import platform
import signal
import atexit

# 配置ImageMagick环境变量（用于MoviePy字幕功能）
os.environ["IMAGEMAGICK_BINARY"] = r"ImageMagick-7.1.2-Q16-HDRI\magick.exe"

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入公共组件
from common.app_base import AppBase

# 导入功能模块
from modules.text_search import TextSearchWindow
from modules.text_processor import TextProcessorWindow
from modules.voice_tts import VoiceTTSWindow
from modules.video_composer import VideoComposerWindow
from modules.batch_rename import BatchRenameWindow

# 导入配置管理器
from modules.voice_tts.config import config_manager

class AIVoiceApp(AppBase):
    def __init__(self, root):
        super().__init__(root, "AI 配音助手")

        # 存储模块实例，保持状态
        self.modules = {}

        # 当前活动模块ID
        self.active_module_id = None

        # 侧边栏按钮字典
        self.sidebar_buttons = {}

        # 初始化界面
        self.init_sidebar()
        self.init_content_area()

        # 检查FFmpeg配置
        self.check_ffmpeg_configuration()

        # 应用进程ID，用于清理
        self.app_pid = os.getpid()

        # 默认显示文本搜索模块
        self.show_module("text_search")

        # 设置关闭事件处理
        self.setup_close_handler()

        # 注册退出时的清理函数
        atexit.register(self.cleanup_on_exit)

        # 注册信号处理器（处理Ctrl+C等）
        self.setup_signal_handlers()
    
    def init_sidebar(self):
        """初始化侧边栏"""
        # 添加侧边栏按钮
        sidebar_buttons_info = [
            {"id": "text_search", "text": "文本搜索"},
            {"id": "text_processor", "text": "文本处理"},
            {"id": "voice_tts", "text": "AI配音"},
            {"id": "video_composer", "text": "视频合成"},
            {"id": "batch_rename", "text": "批量命名"}
        ]
        
        # 按钮容器
        buttons_container = tk.Frame(self.sidebar, bg=self.sidebar_color)
        buttons_container.pack(fill=tk.X, pady=10)
        
        for btn_info in sidebar_buttons_info:
            # 创建按钮容器框架，包含选中指示器
            btn_container = tk.Frame(buttons_container, bg=self.sidebar_color, padx=10, pady=2)
            btn_container.pack(fill=tk.X, pady=3)
            
            # 选中指示器（左侧竖条）
            indicator = tk.Frame(btn_container, width=4, bg=self.sidebar_color)
            indicator.pack(side=tk.LEFT, fill=tk.Y)
            
            # 按钮
            btn = tk.Button(
                btn_container,
                text=btn_info["text"],
                font=(self.title_font, 12),
                bg=self.sidebar_color,
                fg="white",
                activebackground=self.accent_color,
                activeforeground="white",
                relief=tk.FLAT,
                padx=10,
                pady=8,
                bd=0,
                cursor="hand2",
                command=lambda module_id=btn_info["id"]: self.show_module(module_id)
            )
            btn.pack(fill=tk.X, expand=True, padx=(5, 0))
            
            # 保存按钮和指示器的引用
            self.sidebar_buttons[btn_info["id"]] = {
                "button": btn,
                "indicator": indicator,
                "container": btn_container
            }
    
    def init_content_area(self):
        """初始化内容区域"""
        self.content_frame = tk.Frame(self.content, bg=self.bg_color)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
    
    def show_module(self, module_id):
        """显示指定模块"""
        # 如果是当前已显示的模块，不做任何操作
        if module_id == self.active_module_id:
            return
            
        # 隐藏当前模块
        if self.active_module_id:
            # 重置当前活动按钮样式
            self.reset_sidebar_button_style(self.active_module_id)
            
            # 隐藏当前模块而不销毁
            if self.active_module_id in self.modules and self.modules[self.active_module_id]:
                self.modules[self.active_module_id].frame.pack_forget()
        
        # 设置新的活动模块ID
        self.active_module_id = module_id
        
        # 设置当前按钮选中样式
        self.set_sidebar_button_active(module_id)
        
        # 显示或创建新模块
        if module_id in self.modules and self.modules[module_id]:
            # 已存在，直接显示
            self.modules[module_id].frame.pack(fill=tk.BOTH, expand=True)
        else:
            # 需要创建新模块
            if module_id == "text_search":
                self.modules[module_id] = TextSearchWindow(self.content_frame)
            elif module_id == "text_processor":
                self.modules[module_id] = TextProcessorWindow(self.content_frame)
            elif module_id == "voice_tts":
                self.modules[module_id] = VoiceTTSWindow(self.content_frame)
            elif module_id == "video_composer":
                self.modules[module_id] = VideoComposerWindow(self.content_frame)
            elif module_id == "batch_rename":
                self.modules[module_id] = BatchRenameWindow(self.content_frame)
    
    def set_sidebar_button_active(self, module_id):
        """设置侧边栏按钮为激活状态"""
        if module_id in self.sidebar_buttons:
            button_data = self.sidebar_buttons[module_id]
            # 设置选中指示器
            button_data["indicator"].config(bg=self.accent_color)
            # 设置按钮背景色略微改变，表示选中
            button_data["button"].config(bg="#414a54")  # 稍微亮一点的背景色
            # 设置容器背景
            button_data["container"].config(bg="#2d3439")  # 稍微暗一点的背景色
    
    def reset_sidebar_button_style(self, module_id):
        """重置侧边栏按钮样式"""
        if module_id in self.sidebar_buttons:
            button_data = self.sidebar_buttons[module_id]
            # 重置选中指示器
            button_data["indicator"].config(bg=self.sidebar_color)
            # 重置按钮背景色
            button_data["button"].config(bg=self.sidebar_color)
            # 重置容器背景
            button_data["container"].config(bg=self.sidebar_color)

    def check_ffmpeg_configuration(self):
        """检查FFmpeg配置，如果未配置或无效则强制用户选择"""
        try:
            # 获取当前配置的FFmpeg路径
            ffmpeg_path = config_manager.get_ffmpeg_path()

            # 检查FFmpeg是否可用
            if not self.is_ffmpeg_valid(ffmpeg_path):
                # FFmpeg未配置或无效，显示强制选择对话框
                self.root.after(100, self.show_ffmpeg_setup_dialog)  # 延迟显示，确保主窗口已完全加载
        except Exception as e:
            print(f"检查FFmpeg配置时出错: {e}")
            # 出错时也显示设置对话框
            self.root.after(100, self.show_ffmpeg_setup_dialog)

    def is_ffmpeg_valid(self, ffmpeg_path):
        """检查FFmpeg路径是否有效"""
        if not ffmpeg_path:
            return False

        try:
            # 检查目录是否存在
            if not os.path.exists(ffmpeg_path):
                return False

            # 检查bin目录和ffmpeg.exe是否存在
            bin_dir = os.path.join(ffmpeg_path, "bin")
            if not os.path.exists(bin_dir):
                return False

            ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
            ffprobe_exe = os.path.join(bin_dir, "ffprobe.exe")

            if not (os.path.exists(ffmpeg_exe) and os.path.exists(ffprobe_exe)):
                return False

            # 尝试运行FFmpeg检查是否可执行
            import subprocess
            try:
                result = subprocess.run([ffmpeg_exe, '-version'],
                                      capture_output=True, text=True, timeout=5)
                return result.returncode == 0
            except:
                return False

        except Exception:
            return False

    def show_ffmpeg_setup_dialog(self):
        """显示FFmpeg设置对话框"""
        # 创建模态对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("FFmpeg 配置")
        dialog.geometry("650x600")
        dialog.resizable(True, True)
        dialog.minsize(600, 400)  # 设置最小尺寸
        dialog.transient(self.root)
        dialog.grab_set()  # 模态对话框

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 设置对话框样式
        dialog.configure(bg="#f8f9fa")

        # 标题
        title_label = tk.Label(
            dialog,
            text="FFmpeg 配置",
            font=(self.title_font, 16, "bold"),
            fg="#212529",
            bg="#f8f9fa"
        )
        title_label.pack(pady=(20, 10))

        # 说明文本
        info_text = """软件需要FFmpeg来处理音频和视频文件。

请选择FFmpeg的安装目录（包含bin文件夹的目录）。

目录结构应该是：
选择的目录/
    └── bin/
        ├── ffmpeg.exe
        └── ffprobe.exe

如果您还没有安装FFmpeg，请先下载并解压到任意目录。"""

        info_label = tk.Label(
            dialog,
            text=info_text,
            font=(self.content_font, 11),
            fg="#495057",
            bg="#f8f9fa",
            justify=tk.LEFT,
            wraplength=580
        )
        info_label.pack(pady=15, padx=30)

        # 路径显示和选择
        path_frame = tk.Frame(dialog, bg="#f8f9fa")
        path_frame.pack(pady=25, padx=30, fill=tk.X)

        path_label = tk.Label(
            path_frame,
            text="FFmpeg目录:",
            font=(self.content_font, 11),
            fg="#212529",
            bg="#f8f9fa"
        )
        path_label.pack(anchor=tk.W)

        self.ffmpeg_path_var = tk.StringVar()
        path_entry = tk.Entry(
            path_frame,
            textvariable=self.ffmpeg_path_var,
            font=(self.content_font, 11),
            width=60,
            state="readonly"
        )
        path_entry.pack(fill=tk.X, pady=(8, 15))

        # 按钮框架
        button_frame = tk.Frame(dialog, bg="#f8f9fa")
        button_frame.pack(pady=20)

        # 选择目录按钮
        select_btn = tk.Button(
            button_frame,
            text="选择FFmpeg目录",
            font=(self.content_font, 11),
            bg="#4361ee",
            fg="white",
            padx=25,
            pady=10,
            cursor="hand2",
            command=lambda: self.select_ffmpeg_directory(dialog)
        )
        select_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 确认按钮
        self.confirm_btn = tk.Button(
            button_frame,
            text="确认",
            font=(self.content_font, 11),
            bg="#28a745",
            fg="white",
            padx=25,
            pady=10,
            cursor="hand2",
            state="disabled",
            command=lambda: self.confirm_ffmpeg_setup(dialog)
        )
        self.confirm_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 跳过配置按钮
        skip_btn = tk.Button(
            button_frame,
            text="跳过配置",
            font=(self.content_font, 11),
            bg="#6c757d",
            fg="white",
            padx=25,
            pady=10,
            cursor="hand2",
            command=lambda: self.skip_ffmpeg_setup(dialog)
        )
        skip_btn.pack(side=tk.LEFT)

        # 状态标签
        self.status_label = tk.Label(
            dialog,
            text="请选择FFmpeg目录",
            font=(self.content_font, 10),
            fg="#6c757d",
            bg="#f8f9fa"
        )
        self.status_label.pack(pady=15)

        # 处理对话框关闭事件
        dialog.protocol("WM_DELETE_WINDOW", lambda: self.on_ffmpeg_dialog_close(dialog))

    def on_ffmpeg_dialog_close(self, dialog):
        """处理FFmpeg配置对话框关闭事件"""
        # 询问用户是否要退出软件
        result = messagebox.askyesno(
            "确认退出",
            "FFmpeg未配置，软件的音频和视频处理功能将无法正常使用。\n\n是否要退出软件？",
            parent=dialog
        )

        if result:
            # 用户选择退出
            dialog.destroy()
            self.root.quit()  # 退出整个应用程序
        # 如果用户选择不退出，则什么都不做，对话框保持打开状态

    def skip_ffmpeg_setup(self, dialog):
        """跳过FFmpeg配置"""
        # 询问用户确认
        result = messagebox.askyesno(
            "跳过配置",
            "跳过FFmpeg配置后，软件的音频和视频处理功能将无法正常使用。\n\n您可以稍后在设置中重新配置FFmpeg。\n\n确定要跳过吗？",
            parent=dialog
        )

        if result:
            # 用户确认跳过
            dialog.destroy()
            # 显示提示信息
            messagebox.showinfo(
                "提示",
                "已跳过FFmpeg配置。\n\n您可以在AI配音模块中点击'选择FFmpeg路径'来重新配置。",
                parent=self.root
            )

    def select_ffmpeg_directory(self, dialog):
        """选择FFmpeg目录"""
        dir_path = filedialog.askdirectory(
            title="选择FFmpeg目录（包含bin文件夹的目录）",
            parent=dialog
        )

        if dir_path:
            self.ffmpeg_path_var.set(dir_path)

            # 验证选择的目录
            if self.is_ffmpeg_valid(dir_path):
                self.status_label.config(
                    text="✓ FFmpeg目录有效",
                    fg="#28a745"
                )
                self.confirm_btn.config(state="normal")
            else:
                self.status_label.config(
                    text="✗ 所选目录中未找到有效的FFmpeg程序，请检查目录结构",
                    fg="#dc3545"
                )
                self.confirm_btn.config(state="disabled")

    def confirm_ffmpeg_setup(self, dialog):
        """确认FFmpeg设置"""
        ffmpeg_path = self.ffmpeg_path_var.get()

        if self.is_ffmpeg_valid(ffmpeg_path):
            # 保存配置
            config_manager.set_ffmpeg_path(ffmpeg_path)

            # 关闭对话框
            dialog.destroy()

            # 显示成功消息
            messagebox.showinfo(
                "设置成功",
                "FFmpeg配置已保存！\n软件现在可以正常使用音频和视频处理功能。",
                parent=self.root
            )
        else:
            messagebox.showerror(
                "设置失败",
                "所选目录无效，请重新选择正确的FFmpeg目录。",
                parent=dialog
            )

    def setup_close_handler(self):
        """设置关闭事件处理"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            # 处理Ctrl+C (SIGINT)
            signal.signal(signal.SIGINT, self.signal_handler)

            # 在Windows上处理关闭信号
            if platform.system() == "Windows":
                signal.signal(signal.SIGTERM, self.signal_handler)
            else:
                # 在Unix系统上处理更多信号
                signal.signal(signal.SIGTERM, self.signal_handler)
                signal.signal(signal.SIGHUP, self.signal_handler)

        except Exception as e:
            print(f"设置信号处理器失败: {e}")

    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"接收到信号 {signum}，开始清理...")
        try:
            # 强制清理，不询问用户
            self.stop_all_tasks()
            self.kill_all_ffmpeg_processes()
            self.cleanup_app_processes()
        except:
            pass
        finally:
            # 强制退出
            os._exit(1)

    def is_any_task_running(self):
        """检查是否有任务正在运行"""
        try:
            running_tasks = []

            # 检查AI配音模块
            if "voice_tts" in self.modules:
                voice_module = self.modules["voice_tts"]
                if hasattr(voice_module, 'is_processing') and voice_module.is_processing:
                    # 获取更详细的状态信息
                    status = "AI配音任务正在进行中"
                    if hasattr(voice_module, 'status_label'):
                        try:
                            current_status = voice_module.status_label.cget("text")
                            if current_status:
                                status = f"AI配音：{current_status}"
                        except:
                            pass
                    running_tasks.append(status)

            # 检查视频合成模块
            if "video_composer" in self.modules:
                video_module = self.modules["video_composer"]
                if hasattr(video_module, 'is_processing') and video_module.is_processing:
                    # 获取更详细的状态信息
                    status = "视频合成任务正在进行中"
                    if hasattr(video_module, 'status_label'):
                        try:
                            current_status = video_module.status_label.cget("text")
                            if current_status:
                                status = f"视频合成：{current_status}"
                        except:
                            pass
                    running_tasks.append(status)

            if running_tasks:
                return True, "\n".join(running_tasks)
            else:
                return False, ""

        except Exception as e:
            print(f"检查任务状态时出错: {e}")
            return False, ""

    def on_closing(self):
        """处理窗口关闭事件"""
        try:
            # 检查是否有任务正在运行
            is_running, task_name = self.is_any_task_running()

            if is_running:
                # 有任务正在运行，询问用户是否确认关闭
                message = f"检测到以下任务正在运行：\n\n{task_name}\n\n"
                message += "确定要关闭软件吗？\n\n"
                message += "⚠️ 关闭后将会：\n"
                message += "• 强制终止所有正在进行的任务\n"
                message += "• 清理所有FFmpeg后台进程\n"
                message += "• 可能丢失未保存的进度"

                result = messagebox.askyesno(
                    "确认关闭 - 有任务正在运行",
                    message,
                    icon="warning"
                )

                if not result:
                    return  # 用户选择不关闭

            # 用户确认关闭或没有任务运行，开始清理
            self.cleanup_and_exit()

        except Exception as e:
            print(f"关闭处理时出错: {e}")
            # 即使出错也要尝试清理
            self.cleanup_and_exit()

    def cleanup_and_exit(self):
        """清理资源并退出"""
        # 显示关闭进度弹窗
        self.show_closing_dialog()

        try:
            print("开始清理应用资源...")

            # 停止所有模块的任务
            self.update_closing_status("正在停止运行中的任务...")
            self.stop_all_tasks()

            # 清理所有FFmpeg进程
            self.update_closing_status("正在清理FFmpeg进程...")
            self.kill_all_ffmpeg_processes()

            # 清理应用相关进程
            self.update_closing_status("正在清理应用进程...")
            self.cleanup_app_processes()

            self.update_closing_status("资源清理完成，正在退出...")
            print("资源清理完成")

        except Exception as e:
            print(f"清理资源时出错: {e}")
        finally:
            # 强制退出
            self.root.quit()
            self.root.destroy()

    def stop_all_tasks(self):
        """停止所有模块的任务"""
        try:
            # 停止AI配音任务
            if "voice_tts" in self.modules:
                voice_module = self.modules["voice_tts"]
                if hasattr(voice_module, 'processor') and hasattr(voice_module.processor, 'stop_processing'):
                    print("停止AI配音任务...")
                    voice_module.processor.stop_processing()

            # 停止视频合成任务
            if "video_composer" in self.modules:
                video_module = self.modules["video_composer"]
                if hasattr(video_module, 'processor') and hasattr(video_module.processor, 'stop_processing'):
                    print("停止视频合成任务...")
                    video_module.processor.stop_processing()

        except Exception as e:
            print(f"停止任务时出错: {e}")

    def kill_all_ffmpeg_processes(self):
        """终止所有FFmpeg相关进程"""
        try:
            # 首先使用模块的进程管理器
            try:
                from modules.voice_tts.core import get_ffmpeg_manager
                voice_manager = get_ffmpeg_manager()
                voice_manager.kill_all_ffmpeg_processes()
                print("AI配音FFmpeg进程清理完成")
            except Exception as e:
                print(f"AI配音FFmpeg进程清理失败: {e}")

            try:
                from modules.video_composer.core import get_video_ffmpeg_manager
                video_manager = get_video_ffmpeg_manager()
                video_manager.kill_all_video_ffmpeg_processes()
                print("视频合成FFmpeg进程清理完成")
            except Exception as e:
                print(f"视频合成FFmpeg进程清理失败: {e}")

            # 然后进行全局FFmpeg进程清理（兜底）
            killed_count = 0

            # 查找并终止所有FFmpeg进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower() if proc_info['name'] else ''

                    # 检查是否是FFmpeg相关进程
                    if ('ffmpeg' in proc_name or 'ffprobe' in proc_name):
                        proc.terminate()
                        killed_count += 1
                        print(f"全局清理：终止FFmpeg进程: {proc_name} PID {proc_info['pid']}")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            # 等待进程终止
            import time
            time.sleep(1)

            # 强制杀死残留的FFmpeg进程
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower() if proc_info['name'] else ''

                    if ('ffmpeg' in proc_name or 'ffprobe' in proc_name):
                        proc.kill()
                        killed_count += 1
                        print(f"全局清理：强制杀死FFmpeg进程: {proc_name} PID {proc_info['pid']}")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            if killed_count > 0:
                print(f"全局清理：总共终止了 {killed_count} 个FFmpeg进程")
            else:
                print("全局清理：没有发现需要终止的FFmpeg进程")

        except Exception as e:
            print(f"终止FFmpeg进程时出错: {e}")

    def cleanup_app_processes(self):
        """清理应用相关进程"""
        try:
            # 获取当前进程的所有子进程
            current_process = psutil.Process(self.app_pid)
            children = current_process.children(recursive=True)

            # 终止所有子进程
            for child in children:
                try:
                    child.terminate()
                    print(f"终止子进程: PID {child.pid}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # 等待子进程终止
            import time
            time.sleep(0.5)

            # 强制杀死残留的子进程
            for child in children:
                try:
                    if child.is_running():
                        child.kill()
                        print(f"强制杀死子进程: PID {child.pid}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

        except Exception as e:
            print(f"清理应用进程时出错: {e}")

    def show_closing_dialog(self):
        """显示关闭进度弹窗"""
        try:
            # 创建关闭进度弹窗
            self.closing_dialog = tk.Toplevel(self.root)
            self.closing_dialog.title("正在关闭")
            self.closing_dialog.geometry("300x150")
            self.closing_dialog.resizable(False, False)

            # 设置弹窗居中
            self.closing_dialog.transient(self.root)
            self.closing_dialog.grab_set()

            # 计算居中位置
            x = (self.closing_dialog.winfo_screenwidth() // 2) - (300 // 2)
            y = (self.closing_dialog.winfo_screenheight() // 2) - (150 // 2)
            self.closing_dialog.geometry(f"300x150+{x}+{y}")

            # 禁用关闭按钮
            self.closing_dialog.protocol("WM_DELETE_WINDOW", lambda: None)

            # 创建主框架
            main_frame = tk.Frame(self.closing_dialog, bg="#f8f9fa")
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题标签
            title_label = tk.Label(
                main_frame,
                text="正在关闭软件...",
                font=("微软雅黑", 14, "bold"),
                bg="#f8f9fa",
                fg="#495057"
            )
            title_label.pack(pady=(10, 15))

            # 进度标签
            self.closing_status_label = tk.Label(
                main_frame,
                text="正在清理资源，请稍候...",
                font=("微软雅黑", 10),
                bg="#f8f9fa",
                fg="#6c757d"
            )
            self.closing_status_label.pack(pady=(0, 15))

            # 转圈动画标签
            self.closing_spinner_label = tk.Label(
                main_frame,
                text="●",
                font=("微软雅黑", 20),
                bg="#f8f9fa",
                fg="#007bff"
            )
            self.closing_spinner_label.pack()

            # 启动转圈动画
            self.start_closing_spinner()

            # 更新弹窗显示
            self.closing_dialog.update()

        except Exception as e:
            print(f"显示关闭弹窗时出错: {e}")

    def start_closing_spinner(self):
        """启动关闭弹窗的转圈动画"""
        try:
            self.closing_spinner_chars = ["●", "○", "◐", "◑", "◒", "◓"]
            self.closing_spinner_index = 0
            self.update_closing_spinner()
        except Exception as e:
            print(f"启动转圈动画时出错: {e}")

    def update_closing_spinner(self):
        """更新转圈动画"""
        try:
            if hasattr(self, 'closing_spinner_label') and self.closing_spinner_label.winfo_exists():
                # 更新转圈字符
                char = self.closing_spinner_chars[self.closing_spinner_index]
                self.closing_spinner_label.config(text=char)

                # 更新索引
                self.closing_spinner_index = (self.closing_spinner_index + 1) % len(self.closing_spinner_chars)

                # 安排下次更新
                self.root.after(200, self.update_closing_spinner)
        except Exception:
            pass  # 静默处理，避免关闭时的错误

    def update_closing_status(self, message):
        """更新关闭状态信息"""
        try:
            if hasattr(self, 'closing_status_label') and self.closing_status_label.winfo_exists():
                self.closing_status_label.config(text=message)
                self.closing_dialog.update()
                # 给用户一点时间看到状态更新
                import time
                time.sleep(0.3)
        except Exception:
            pass  # 静默处理，避免关闭时的错误

    def cleanup_on_exit(self):
        """退出时的清理函数（atexit注册）"""
        try:
            # 这个函数在程序异常退出时也会被调用
            self.kill_all_ffmpeg_processes()
            self.cleanup_app_processes()
        except:
            pass  # 静默处理，避免退出时的错误信息

if __name__ == "__main__":
    root = tk.Tk()
    app = AIVoiceApp(root)
    root.mainloop()